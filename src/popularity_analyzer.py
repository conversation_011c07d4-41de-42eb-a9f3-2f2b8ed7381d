"""
物品流行度分析模块

负责计算和分析物品流行度分布，为偏差修正提供基础数据
"""

import numpy as np
import pandas as pd
from typing import Dict, Tuple, List
from collections import Counter


class PopularityAnalyzer:
    """物品流行度分析器"""
    
    def __init__(self):
        """初始化分析器"""
        pass
    
    def compute_item_popularity(self, df: pd.DataFrame) -> <PERSON>ple[Dict[int, float], Dict[int, float]]:
        """
        计算物品流行度和对数概率
        
        Args:
            df: 包含交互数据的DataFrame
            
        Returns:
            Tuple[Dict[int, float], Dict[int, float]]: (流行度字典, 对数概率字典)
        """
        # 计算每个物品的交互次数
        item_counts = df['movie_id'].value_counts().to_dict()
        total_interactions = len(df)
        
        # 计算流行度概率
        item_popularity = {}
        item_log_prob = {}
        
        for movie_id, count in item_counts.items():
            popularity = count / total_interactions
            item_popularity[movie_id] = popularity
            item_log_prob[movie_id] = np.log(popularity)
        
        return item_popularity, item_log_prob
    
    def get_popularity_statistics(self, item_popularity: Dict[int, float]) -> Dict[str, float]:
        """
        获取流行度统计信息
        
        Args:
            item_popularity: 物品流行度字典
            
        Returns:
            Dict[str, float]: 统计信息
        """
        popularities = list(item_popularity.values())
        
        stats = {
            'count': len(popularities),
            'mean': np.mean(popularities),
            'std': np.std(popularities),
            'min': np.min(popularities),
            'max': np.max(popularities),
            'median': np.median(popularities),
            'q25': np.percentile(popularities, 25),
            'q75': np.percentile(popularities, 75),
            'ratio': np.max(popularities) / np.min(popularities)
        }
        
        return stats
    
    def categorize_items_by_popularity(self, item_popularity: Dict[int, float], 
                                     thresholds: List[float] = None) -> Dict[str, List[int]]:
        """
        根据流行度对物品进行分类
        
        Args:
            item_popularity: 物品流行度字典
            thresholds: 分类阈值列表，默认使用四分位数
            
        Returns:
            Dict[str, List[int]]: 分类结果
        """
        if thresholds is None:
            popularities = list(item_popularity.values())
            thresholds = [
                np.percentile(popularities, 25),
                np.percentile(popularities, 50),
                np.percentile(popularities, 75)
            ]
        
        categories = {
            'hot': [],      # 热门 (>75%)
            'warm': [],     # 较热门 (50%-75%)
            'medium': [],   # 中等 (25%-50%)
            'cold': []      # 冷门 (<25%)
        }
        
        for movie_id, popularity in item_popularity.items():
            if popularity > thresholds[2]:
                categories['hot'].append(movie_id)
            elif popularity > thresholds[1]:
                categories['warm'].append(movie_id)
            elif popularity > thresholds[0]:
                categories['medium'].append(movie_id)
            else:
                categories['cold'].append(movie_id)
        
        return categories
    
    def get_top_bottom_items(self, item_popularity: Dict[int, float], 
                           top_k: int = 10) -> Tuple[List[Tuple[int, float]], List[Tuple[int, float]]]:
        """
        获取最热门和最冷门的物品
        
        Args:
            item_popularity: 物品流行度字典
            top_k: 返回的物品数量
            
        Returns:
            Tuple[List[Tuple[int, float]], List[Tuple[int, float]]]: (最热门, 最冷门)
        """
        sorted_items = sorted(item_popularity.items(), key=lambda x: x[1], reverse=True)
        
        top_items = sorted_items[:top_k]
        bottom_items = sorted_items[-top_k:]
        
        return top_items, bottom_items
    
    def compute_gini_coefficient(self, item_popularity: Dict[int, float]) -> float:
        """
        计算基尼系数，衡量流行度分布的不平等程度
        
        Args:
            item_popularity: 物品流行度字典
            
        Returns:
            float: 基尼系数 (0-1之间，越大越不平等)
        """
        popularities = sorted(item_popularity.values())
        n = len(popularities)
        
        # 计算基尼系数
        cumsum = np.cumsum(popularities)
        gini = (n + 1 - 2 * np.sum(cumsum) / cumsum[-1]) / n
        
        return gini
    
    def analyze_long_tail_distribution(self, item_popularity: Dict[int, float]) -> Dict[str, any]:
        """
        分析长尾分布特征
        
        Args:
            item_popularity: 物品流行度字典
            
        Returns:
            Dict[str, any]: 长尾分布分析结果
        """
        sorted_items = sorted(item_popularity.items(), key=lambda x: x[1], reverse=True)
        popularities = [item[1] for item in sorted_items]
        
        # 计算累积分布
        cumulative_popularity = np.cumsum(popularities)
        total_popularity = cumulative_popularity[-1]
        
        # 找到占80%流行度的物品数量 (帕累托原理)
        items_80_percent = np.searchsorted(cumulative_popularity, 0.8 * total_popularity) + 1
        
        # 计算头部、中部、尾部的比例
        head_ratio = items_80_percent / len(sorted_items)
        
        analysis = {
            'total_items': len(sorted_items),
            'items_80_percent': items_80_percent,
            'head_ratio': head_ratio,
            'tail_ratio': 1 - head_ratio,
            'gini_coefficient': self.compute_gini_coefficient(item_popularity),
            'popularity_concentration': popularities[0] / np.mean(popularities)
        }
        
        return analysis
    
    def print_popularity_stats(self, item_popularity: Dict[int, float], 
                             item_log_prob: Dict[int, float]):
        """
        打印流行度统计信息
        
        Args:
            item_popularity: 物品流行度字典
            item_log_prob: 物品对数概率字典
        """
        stats = self.get_popularity_statistics(item_popularity)
        top_items, bottom_items = self.get_top_bottom_items(item_popularity, 5)
        long_tail_analysis = self.analyze_long_tail_distribution(item_popularity)
        
        print(f"✅ 流行度计算完成")
        print(f"   物品总数: {stats['count']}")
        print(f"   最高流行度: {stats['max']:.6f}")
        print(f"   最低流行度: {stats['min']:.6f}")
        print(f"   流行度比例: {stats['ratio']:.1f}:1")
        print(f"   基尼系数: {long_tail_analysis['gini_coefficient']:.4f}")
        
        log_probs = list(item_log_prob.values())
        print(f"   log概率范围: {min(log_probs):.4f} ~ {max(log_probs):.4f}")
        
        print("\n🔥 最热门的5部电影:")
        for i, (movie_id, pop) in enumerate(top_items):
            log_prob = item_log_prob[movie_id]
            print(f"   {i+1}. 电影{movie_id}: 流行度={pop:.6f}, log(pi)={log_prob:.4f}")
        
        print("\n❄️ 最冷门的5部电影:")
        for i, (movie_id, pop) in enumerate(bottom_items):
            log_prob = item_log_prob[movie_id]
            print(f"   {i+1}. 电影{movie_id}: 流行度={pop:.6f}, log(pi)={log_prob:.4f}")
        
        print(f"\n📊 长尾分布分析:")
        print(f"   80%流行度集中在前 {long_tail_analysis['items_80_percent']} 部电影")
        print(f"   头部比例: {long_tail_analysis['head_ratio']:.2%}")
        print(f"   尾部比例: {long_tail_analysis['tail_ratio']:.2%}")
        print(f"   流行度集中度: {long_tail_analysis['popularity_concentration']:.1f}x")
        
        print("\n💡 观察: 物品流行度呈现典型的长尾分布，少数热门物品占据大部分交互")
        print("💡 问题: 传统负采样容易采样到热门物品，导致模型偏向推荐热门内容")
        print("💡 解决: 通过 cos(a,bi) - log(pi) 修正，平衡热门和冷门物品的学习权重")
    
    def save_popularity_data(self, item_popularity: Dict[int, float], 
                           item_log_prob: Dict[int, float], output_path: str):
        """
        保存流行度数据
        
        Args:
            item_popularity: 物品流行度字典
            item_log_prob: 物品对数概率字典
            output_path: 输出文件路径
        """
        df = pd.DataFrame({
            'movie_id': list(item_popularity.keys()),
            'popularity': list(item_popularity.values()),
            'log_prob': list(item_log_prob.values())
        })
        
        df.to_csv(output_path, index=False)
        print(f"✅ 流行度数据已保存到: {output_path}")
    
    def load_popularity_data(self, input_path: str) -> Tuple[Dict[int, float], Dict[int, float]]:
        """
        加载流行度数据
        
        Args:
            input_path: 输入文件路径
            
        Returns:
            Tuple[Dict[int, float], Dict[int, float]]: (流行度字典, 对数概率字典)
        """
        df = pd.read_csv(input_path)
        
        item_popularity = dict(zip(df['movie_id'], df['popularity']))
        item_log_prob = dict(zip(df['movie_id'], df['log_prob']))
        
        print(f"✅ 流行度数据已从 {input_path} 加载")
        return item_popularity, item_log_prob
