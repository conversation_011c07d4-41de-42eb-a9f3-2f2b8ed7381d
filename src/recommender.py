"""
推荐引擎模块

实现召回和排序的完整推荐流程
"""

import numpy as np
import tensorflow as tf
from typing import Dict, List, Tuple, Optional, Any


class RecommendationEngine:
    """推荐引擎"""
    
    def __init__(self):
        """初始化推荐引擎"""
        pass
    
    def recommend(self, model, user_features: Dict[str, Any], 
                 candidate_movies: List[Dict[str, Any]], 
                 k_recall: int = 20, k_rank: int = 10) -> List[Dict[str, Any]]:
        """
        执行推荐流程：召回 + 排序
        
        Args:
            model: 训练好的双塔模型
            user_features: 用户特征
            candidate_movies: 候选电影列表
            k_recall: 召回阶段Top-K
            k_rank: 排序阶段Top-K
            
        Returns:
            List[Dict[str, Any]]: 推荐结果列表
        """
        recommendations = []
        
        for movie in candidate_movies:
            # 构建输入特征
            features = {
                'user_id': tf.constant([user_features['user_id']]),
                'movie_id': tf.constant([movie['movie_id']]),
                'gender': tf.constant([user_features['gender']]),
                'age': tf.constant([user_features['age']]),
                'occupation': tf.constant([user_features['occupation']]),
                'main_genre': tf.constant([movie['main_genre']]),
                'year': tf.constant([movie['year']])
            }
            
            # 推理模式，使用原始cosine相似度
            outputs = model.predict(features, training=False)
            
            similarity = outputs['cosine_similarity'].numpy()[0][0]
            predicted_rating = outputs['predicted_rating'].numpy()[0][0]
            
            recommendations.append({
                'movie_info': movie,
                'similarity': similarity,
                'predicted_rating': predicted_rating
            })
        
        # 召回阶段：按相似度排序
        recommendations.sort(key=lambda x: x['similarity'], reverse=True)
        recalled_items = recommendations[:k_recall]
        
        # 排序阶段：按预测评分排序
        recalled_items.sort(key=lambda x: x['predicted_rating'], reverse=True)
        final_recommendations = recalled_items[:k_rank]
        
        return final_recommendations
    
    def batch_recommend(self, model, user_features_list: List[Dict[str, Any]], 
                       candidate_movies: List[Dict[str, Any]], 
                       k_recall: int = 20, k_rank: int = 10) -> List[List[Dict[str, Any]]]:
        """
        批量推荐
        
        Args:
            model: 训练好的双塔模型
            user_features_list: 用户特征列表
            candidate_movies: 候选电影列表
            k_recall: 召回阶段Top-K
            k_rank: 排序阶段Top-K
            
        Returns:
            List[List[Dict[str, Any]]]: 每个用户的推荐结果列表
        """
        all_recommendations = []
        
        for user_features in user_features_list:
            recommendations = self.recommend(
                model, user_features, candidate_movies, k_recall, k_rank
            )
            all_recommendations.append(recommendations)
        
        return all_recommendations
    
    def get_user_item_similarity(self, model, user_features: Dict[str, Any], 
                                item_features: Dict[str, Any]) -> float:
        """
        计算用户-物品相似度
        
        Args:
            model: 训练好的双塔模型
            user_features: 用户特征
            item_features: 物品特征
            
        Returns:
            float: 相似度分数
        """
        # 构建完整特征
        features = {
            'user_id': tf.constant([user_features['user_id']]),
            'movie_id': tf.constant([item_features['movie_id']]),
            'gender': tf.constant([user_features['gender']]),
            'age': tf.constant([user_features['age']]),
            'occupation': tf.constant([user_features['occupation']]),
            'main_genre': tf.constant([item_features['main_genre']]),
            'year': tf.constant([item_features['year']])
        }
        
        outputs = model.predict(features, training=False)
        similarity = outputs['cosine_similarity'].numpy()[0][0]
        
        return float(similarity)
    
    def get_similar_items(self, model, target_item: Dict[str, Any], 
                         candidate_items: List[Dict[str, Any]], 
                         k: int = 10) -> List[Dict[str, Any]]:
        """
        获取相似物品
        
        Args:
            model: 训练好的双塔模型
            target_item: 目标物品
            candidate_items: 候选物品列表
            k: 返回的相似物品数量
            
        Returns:
            List[Dict[str, Any]]: 相似物品列表
        """
        # 获取目标物品的嵌入
        target_features = {
            'user_id': tf.constant([1]),  # 占位符
            'movie_id': tf.constant([target_item['movie_id']]),
            'gender': tf.constant([0]),  # 占位符
            'age': tf.constant([0]),  # 占位符
            'occupation': tf.constant([0]),  # 占位符
            'main_genre': tf.constant([target_item['main_genre']]),
            'year': tf.constant([target_item['year']])
        }
        
        target_embedding = model.get_item_embedding(target_features)
        
        similarities = []
        for candidate in candidate_items:
            if candidate['movie_id'] == target_item['movie_id']:
                continue  # 跳过自己
            
            candidate_features = {
                'user_id': tf.constant([1]),  # 占位符
                'movie_id': tf.constant([candidate['movie_id']]),
                'gender': tf.constant([0]),  # 占位符
                'age': tf.constant([0]),  # 占位符
                'occupation': tf.constant([0]),  # 占位符
                'main_genre': tf.constant([candidate['main_genre']]),
                'year': tf.constant([candidate['year']])
            }
            
            candidate_embedding = model.get_item_embedding(candidate_features)
            
            # 计算cosine相似度
            similarity = tf.keras.utils.cosine_similarity(
                target_embedding, candidate_embedding, axis=-1
            ).numpy()[0]
            
            similarities.append({
                'item_info': candidate,
                'similarity': float(similarity)
            })
        
        # 按相似度排序
        similarities.sort(key=lambda x: x['similarity'], reverse=True)
        
        return similarities[:k]
    
    def explain_recommendation(self, model, user_features: Dict[str, Any], 
                             item_features: Dict[str, Any]) -> Dict[str, Any]:
        """
        推荐解释
        
        Args:
            model: 训练好的双塔模型
            user_features: 用户特征
            item_features: 物品特征
            
        Returns:
            Dict[str, Any]: 推荐解释信息
        """
        # 构建完整特征
        features = {
            'user_id': tf.constant([user_features['user_id']]),
            'movie_id': tf.constant([item_features['movie_id']]),
            'gender': tf.constant([user_features['gender']]),
            'age': tf.constant([user_features['age']]),
            'occupation': tf.constant([user_features['occupation']]),
            'main_genre': tf.constant([item_features['main_genre']]),
            'year': tf.constant([item_features['year']])
        }
        
        outputs = model.predict(features, training=False)
        
        explanation = {
            'user_id': user_features['user_id'],
            'movie_id': item_features['movie_id'],
            'cosine_similarity': float(outputs['cosine_similarity'].numpy()[0][0]),
            'predicted_rating': float(outputs['predicted_rating'].numpy()[0][0]),
            'user_vector_norm': float(tf.norm(outputs['user_vector']).numpy()),
            'item_vector_norm': float(tf.norm(outputs['item_vector']).numpy()),
            'match_factors': {
                'genre': item_features['main_genre'],
                'year': item_features['year'],
                'user_profile': {
                    'gender': user_features['gender'],
                    'age': user_features['age'],
                    'occupation': user_features['occupation']
                }
            }
        }
        
        return explanation
    
    def evaluate_recommendation_quality(self, recommendations: List[Dict[str, Any]], 
                                      ground_truth: List[int] = None) -> Dict[str, float]:
        """
        评估推荐质量
        
        Args:
            recommendations: 推荐结果列表
            ground_truth: 真实喜欢的物品ID列表
            
        Returns:
            Dict[str, float]: 评估指标
        """
        metrics = {}
        
        # 推荐多样性
        genres = [rec['movie_info']['main_genre'] for rec in recommendations]
        unique_genres = len(set(genres))
        metrics['genre_diversity'] = unique_genres / len(genres) if genres else 0
        
        # 年份分布
        years = [rec['movie_info']['year'] for rec in recommendations]
        metrics['year_std'] = float(np.std(years)) if years else 0
        
        # 相似度分布
        similarities = [rec['similarity'] for rec in recommendations]
        metrics['similarity_mean'] = float(np.mean(similarities)) if similarities else 0
        metrics['similarity_std'] = float(np.std(similarities)) if similarities else 0
        
        # 预测评分分布
        ratings = [rec['predicted_rating'] for rec in recommendations]
        metrics['rating_mean'] = float(np.mean(ratings)) if ratings else 0
        metrics['rating_std'] = float(np.std(ratings)) if ratings else 0
        
        # 如果有真实标签，计算准确性指标
        if ground_truth:
            recommended_ids = [rec['movie_info']['movie_id'] for rec in recommendations]
            hits = len(set(recommended_ids) & set(ground_truth))
            metrics['precision'] = hits / len(recommended_ids) if recommended_ids else 0
            metrics['recall'] = hits / len(ground_truth) if ground_truth else 0
            
            if metrics['precision'] + metrics['recall'] > 0:
                metrics['f1'] = 2 * metrics['precision'] * metrics['recall'] / (metrics['precision'] + metrics['recall'])
            else:
                metrics['f1'] = 0
        
        return metrics
    
    def print_recommendations(self, recommendations: List[Dict[str, Any]], 
                            user_features: Dict[str, Any], 
                            item_popularity: Dict[int, float] = None):
        """
        打印推荐结果
        
        Args:
            recommendations: 推荐结果列表
            user_features: 用户特征
            item_popularity: 物品流行度字典
        """
        print(f"\n🎯 用户 {user_features['user_id']} 的推荐结果:")
        print(f"   用户信息: 性别={user_features['gender']}, 年龄={user_features['age']}, 职业={user_features['occupation']}")
        print(f"   推荐数量: {len(recommendations)}")
        
        for i, rec in enumerate(recommendations):
            movie_id = rec['movie_info']['movie_id']
            popularity = item_popularity.get(movie_id, 0) if item_popularity else 0
            
            print(f"   {i+1:2d}. 电影{movie_id} | "
                  f"类型:{rec['movie_info']['main_genre']} | "
                  f"年份:{rec['movie_info']['year']:.0f} | "
                  f"相似度:{rec['similarity']:.4f} | "
                  f"评分:{rec['predicted_rating']:.2f}")
            
            if item_popularity:
                print(f"       流行度:{popularity:.6f}")
        
        # 推荐质量评估
        quality = self.evaluate_recommendation_quality(recommendations)
        print(f"\n📊 推荐质量指标:")
        print(f"   类型多样性: {quality['genre_diversity']:.3f}")
        print(f"   年份标准差: {quality['year_std']:.1f}")
        print(f"   平均相似度: {quality['similarity_mean']:.4f}")
        print(f"   平均预测评分: {quality['rating_mean']:.2f}")
