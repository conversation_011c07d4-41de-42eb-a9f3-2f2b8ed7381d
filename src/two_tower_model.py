"""
双塔模型模块

实现用户塔和物品塔的深度学习模型，支持传统训练和改进的偏差修正训练
"""

import os
import numpy as np
import tensorflow as tf
from typing import Dict, List, Tuple, Optional, Any


class TwoTowerModel:
    """双塔推荐模型"""
    
    def __init__(self, vocabs: Dict[str, List], embedding_dim: int = 64):
        """
        初始化双塔模型
        
        Args:
            vocabs: 词汇表字典
            embedding_dim: 嵌入维度
        """
        self.vocabs = vocabs
        self.embedding_dim = embedding_dim
        self.model = None
        self._build_model()
    
    def _build_model(self):
        """构建双塔模型"""
        # 输入层
        inputs = {
            'user_id': tf.keras.layers.Input(shape=(), dtype=tf.int64, name='user_id'),
            'movie_id': tf.keras.layers.Input(shape=(), dtype=tf.int64, name='movie_id'),
            'gender': tf.keras.layers.Input(shape=(), dtype=tf.int64, name='gender'),
            'age': tf.keras.layers.Input(shape=(), dtype=tf.int64, name='age'),
            'occupation': tf.keras.layers.Input(shape=(), dtype=tf.int64, name='occupation'),
            'main_genre': tf.keras.layers.Input(shape=(), dtype=tf.string, name='main_genre'),
            'year': tf.keras.layers.Input(shape=(), dtype=tf.float32, name='year')
        }
        
        # 用户塔嵌入层
        user_embedding = tf.keras.layers.Embedding(
            len(self.vocabs['user_id']), self.embedding_dim, name='user_embedding'
        )(inputs['user_id'])
        
        gender_embedding = tf.keras.layers.Embedding(
            len(self.vocabs['gender']), self.embedding_dim//4, name='gender_embedding'
        )(inputs['gender'])
        
        age_embedding = tf.keras.layers.Embedding(
            len(self.vocabs['age']), self.embedding_dim//4, name='age_embedding'
        )(inputs['age'])
        
        occupation_embedding = tf.keras.layers.Embedding(
            len(self.vocabs['occupation']), self.embedding_dim//2, name='occupation_embedding'
        )(inputs['occupation'])
        
        # 物品塔嵌入层
        movie_embedding = tf.keras.layers.Embedding(
            len(self.vocabs['movie_id']), self.embedding_dim, name='movie_embedding'
        )(inputs['movie_id'])
        
        # 类型嵌入 - 使用StringLookup处理字符串
        genre_lookup = tf.keras.layers.StringLookup(
            vocabulary=self.vocabs['main_genre'], mask_token=None
        )
        genre_ids = genre_lookup(inputs['main_genre'])
        genre_embedding = tf.keras.layers.Embedding(
            len(self.vocabs['main_genre']) + 1, self.embedding_dim//2, name='genre_embedding'
        )(genre_ids)
        
        # 年份处理
        year_expanded = tf.keras.layers.Lambda(lambda x: tf.expand_dims(x, -1))(inputs['year'])
        year_normalized = tf.keras.layers.Normalization()(year_expanded)
        year_embedding = tf.keras.layers.Dense(
            self.embedding_dim//4, activation='relu', name='year_dense'
        )(year_normalized)
        
        # 拼接用户特征
        user_features = tf.keras.layers.Concatenate(name='user_concat')([
            user_embedding, gender_embedding, age_embedding, occupation_embedding
        ])
        
        # 拼接物品特征
        item_features = tf.keras.layers.Concatenate(name='item_concat')([
            movie_embedding, genre_embedding, year_embedding
        ])
        
        # 用户塔
        user_tower = tf.keras.Sequential([
            tf.keras.layers.Dense(self.embedding_dim, activation='relu'),
            tf.keras.layers.Dropout(0.2),
            tf.keras.layers.Dense(self.embedding_dim, activation='relu'),
            tf.keras.layers.LayerNormalization()
        ], name='user_tower')
        
        # 物品塔
        item_tower = tf.keras.Sequential([
            tf.keras.layers.Dense(self.embedding_dim, activation='relu'),
            tf.keras.layers.Dropout(0.2),
            tf.keras.layers.Dense(self.embedding_dim, activation='relu'),
            tf.keras.layers.LayerNormalization()
        ], name='item_tower')
        
        # 通过双塔
        user_vector = user_tower(user_features)
        item_vector = item_tower(item_features)
        
        # 计算cosine相似度
        user_norm = tf.keras.layers.Lambda(lambda x: tf.nn.l2_normalize(x, axis=-1))(user_vector)
        item_norm = tf.keras.layers.Lambda(lambda x: tf.nn.l2_normalize(x, axis=-1))(item_vector)
        cosine_similarity = tf.keras.layers.Lambda(
            lambda x: tf.reduce_sum(x[0] * x[1], axis=-1, keepdims=True)
        )([user_norm, item_norm])

        # 预测评分 (缩放到1-5)
        rating_predictor = tf.keras.layers.Dense(1, activation='sigmoid', name='rating_predictor')
        predicted_rating_raw = rating_predictor(cosine_similarity)
        predicted_rating = tf.keras.layers.Lambda(lambda x: x * 4 + 1)(predicted_rating_raw)
        
        # 创建模型
        self.model = tf.keras.Model(
            inputs=inputs,
            outputs={
                'cosine_similarity': cosine_similarity,
                'predicted_rating': predicted_rating,
                'user_vector': user_vector,
                'item_vector': item_vector
            },
            name='two_tower_model'
        )
    
    def initialize_with_data(self, dataset: tf.data.Dataset):
        """
        使用数据初始化模型，特别是归一化层
        
        Args:
            dataset: 训练数据集
        """
        # 获取一个批次来初始化模型
        sample_batch = next(iter(dataset))
        features, labels = sample_batch
        
        # 初始化年份归一化层
        year_data = features['year']
        year_layer = None
        for layer in self.model.layers:
            if isinstance(layer, tf.keras.layers.Normalization):
                year_layer = layer
                break
        
        if year_layer is not None:
            year_layer.adapt(tf.expand_dims(year_data, -1))
        
        # 调用模型进行初始化
        _ = self.model(features, training=False)
        print("✅ 模型初始化完成")
    
    def compile_model(self, learning_rate: float = 0.001):
        """
        编译模型
        
        Args:
            learning_rate: 学习率
        """
        self.model.compile(
            optimizer=tf.keras.optimizers.Adam(learning_rate=learning_rate),
            loss=self._custom_loss,
            metrics=[tf.keras.metrics.RootMeanSquaredError(name='rmse')]
        )
    
    def _custom_loss(self, y_true, y_pred):
        """
        自定义损失函数，支持偏差修正

        Args:
            y_true: 真实标签
            y_pred: 预测输出字典

        Returns:
            tf.Tensor: 损失值
        """
        # 评分预测损失 (MSE)
        if isinstance(y_pred, dict):
            predicted_rating = y_pred['predicted_rating']
        else:
            predicted_rating = y_pred

        rating_loss = tf.keras.losses.mean_squared_error(y_true, predicted_rating)

        return rating_loss
    
    def train(self, train_dataset: tf.data.Dataset, val_dataset: tf.data.Dataset,
              epochs: int = 10, learning_rate: float = 0.001) -> tf.keras.callbacks.History:
        """
        训练模型
        
        Args:
            train_dataset: 训练数据集
            val_dataset: 验证数据集
            epochs: 训练轮数
            learning_rate: 学习率
            
        Returns:
            tf.keras.callbacks.History: 训练历史
        """
        # 编译模型
        self.compile_model(learning_rate)
        
        # 训练模型
        history = self.model.fit(
            train_dataset,
            validation_data=val_dataset,
            epochs=epochs,
            verbose=1
        )
        
        return history
    
    def predict(self, features: Dict[str, tf.Tensor], training: bool = False) -> Dict[str, tf.Tensor]:
        """
        模型预测
        
        Args:
            features: 输入特征
            training: 是否为训练模式
            
        Returns:
            Dict[str, tf.Tensor]: 预测结果
        """
        return self.model(features, training=training)
    
    def get_user_embedding(self, user_features: Dict[str, tf.Tensor]) -> tf.Tensor:
        """
        获取用户嵌入向量
        
        Args:
            user_features: 用户特征
            
        Returns:
            tf.Tensor: 用户嵌入向量
        """
        outputs = self.model(user_features, training=False)
        return outputs['user_vector']
    
    def get_item_embedding(self, item_features: Dict[str, tf.Tensor]) -> tf.Tensor:
        """
        获取物品嵌入向量
        
        Args:
            item_features: 物品特征
            
        Returns:
            tf.Tensor: 物品嵌入向量
        """
        outputs = self.model(item_features, training=False)
        return outputs['item_vector']
    
    def save(self, save_path: str):
        """
        保存模型
        
        Args:
            save_path: 保存路径
        """
        os.makedirs(save_path, exist_ok=True)
        
        # 保存模型权重
        self.model.save_weights(os.path.join(save_path, 'model_weights'))
        
        # 保存词汇表
        import json
        vocab_path = os.path.join(save_path, 'vocabs.json')
        with open(vocab_path, 'w') as f:
            # 转换为可序列化的格式
            serializable_vocabs = {}
            for key, value in self.vocabs.items():
                if isinstance(value[0], (int, float)):
                    serializable_vocabs[key] = value
                else:
                    serializable_vocabs[key] = [str(v) for v in value]
            json.dump(serializable_vocabs, f, indent=2)
        
        # 保存模型配置
        config = {
            'embedding_dim': self.embedding_dim,
            'vocab_sizes': {key: len(value) for key, value in self.vocabs.items()}
        }
        config_path = os.path.join(save_path, 'config.json')
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"✅ 模型已保存到: {save_path}")
    
    @classmethod
    def load(cls, load_path: str) -> 'TwoTowerModel':
        """
        加载模型
        
        Args:
            load_path: 加载路径
            
        Returns:
            TwoTowerModel: 加载的模型
        """
        import json
        
        # 加载配置
        config_path = os.path.join(load_path, 'config.json')
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        # 加载词汇表
        vocab_path = os.path.join(load_path, 'vocabs.json')
        with open(vocab_path, 'r') as f:
            vocabs = json.load(f)
        
        # 创建模型实例
        model = cls(vocabs, config['embedding_dim'])
        
        # 加载权重
        model.model.load_weights(os.path.join(load_path, 'model_weights'))
        
        print(f"✅ 模型已从 {load_path} 加载")
        return model
    
    def summary(self):
        """打印模型摘要"""
        self.model.summary()
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息
        
        Returns:
            Dict[str, Any]: 模型信息
        """
        info = {
            'embedding_dim': self.embedding_dim,
            'vocab_sizes': {key: len(value) for key, value in self.vocabs.items()},
            'total_parameters': self.model.count_params(),
            'trainable_parameters': sum([tf.keras.backend.count_params(w) for w in self.model.trainable_weights])
        }
        return info
