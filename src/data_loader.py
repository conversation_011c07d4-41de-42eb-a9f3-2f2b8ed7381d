"""
数据加载器模块

负责加载和预处理MovieLens数据集
"""

import os
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional


class MovieLensDataLoader:
    """MovieLens数据加载器"""
    
    def __init__(self, data_dir: str = 'data/ml-1m'):
        """
        初始化数据加载器
        
        Args:
            data_dir: MovieLens数据集目录路径
        """
        self.data_dir = data_dir
        self.ratings_df = None
        self.users_df = None
        self.movies_df = None
    
    def load_data(self) -> bool:
        """
        加载MovieLens数据集
        
        Returns:
            bool: 加载是否成功
        """
        try:
            # 加载评分数据
            ratings_path = os.path.join(self.data_dir, 'ratings.dat')
            self.ratings_df = pd.read_csv(
                ratings_path, 
                sep='::', 
                names=['user_id', 'movie_id', 'rating', 'timestamp'],
                engine='python'
            )
            
            # 加载用户数据
            users_path = os.path.join(self.data_dir, 'users.dat')
            self.users_df = pd.read_csv(
                users_path,
                sep='::',
                names=['user_id', 'gender', 'age', 'occupation', 'zip_code'],
                engine='python'
            )
            
            # 加载电影数据
            movies_path = os.path.join(self.data_dir, 'movies.dat')
            self.movies_df = pd.read_csv(
                movies_path,
                sep='::',
                names=['movie_id', 'title', 'genres'],
                engine='python',
                encoding='latin-1'
            )
            
            print(f"✅ 数据文件加载成功")
            print(f"   评分记录: {len(self.ratings_df):,}")
            print(f"   用户数: {len(self.users_df):,}")
            print(f"   电影数: {len(self.movies_df):,}")
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def preprocess_data(self) -> pd.DataFrame:
        """
        预处理数据
        
        Returns:
            pd.DataFrame: 预处理后的数据
        """
        if self.ratings_df is None:
            raise ValueError("请先调用load_data()加载数据")
        
        # 合并数据
        df = self.ratings_df.merge(self.users_df, on='user_id')
        df = df.merge(self.movies_df, on='movie_id')
        
        # 提取年份
        df['year'] = df['title'].str.extract(r'\((\d{4})\)').astype(float)
        df['year'] = df['year'].fillna(df['year'].median())
        
        # 提取主要类型
        df['main_genre'] = df['genres'].str.split('|').str[0]
        
        # 年龄分组
        age_mapping = {1: 0, 18: 1, 25: 2, 35: 3, 45: 4, 50: 5, 56: 6}
        df['age'] = df['age'].map(age_mapping)
        
        # 性别编码
        df['gender'] = df['gender'].map({'M': 0, 'F': 1})
        
        # 只保留需要的列
        columns_to_keep = [
            'user_id', 'movie_id', 'rating', 'gender', 'age', 
            'occupation', 'main_genre', 'year'
        ]
        df = df[columns_to_keep]
        
        # 删除缺失值
        df = df.dropna()
        
        print(f"✅ 数据预处理完成")
        print(f"   最终记录数: {len(df):,}")
        
        return df
    
    def get_vocabularies(self, df: pd.DataFrame) -> Dict[str, List]:
        """
        获取特征词汇表
        
        Args:
            df: 数据DataFrame
            
        Returns:
            Dict[str, List]: 特征词汇表字典
        """
        vocabs = {
            'user_id': sorted(df['user_id'].unique()),
            'movie_id': sorted(df['movie_id'].unique()),
            'gender': sorted(df['gender'].unique()),
            'age': sorted(df['age'].unique()),
            'occupation': sorted(df['occupation'].unique()),
            'main_genre': sorted(df['main_genre'].unique()),
            'year': sorted(df['year'].unique())
        }
        return vocabs
    
    def get_data_statistics(self, df: pd.DataFrame) -> Dict[str, any]:
        """
        获取数据统计信息
        
        Args:
            df: 数据DataFrame
            
        Returns:
            Dict[str, any]: 统计信息字典
        """
        stats = {
            'total_interactions': len(df),
            'unique_users': df['user_id'].nunique(),
            'unique_movies': df['movie_id'].nunique(),
            'rating_range': (df['rating'].min(), df['rating'].max()),
            'avg_rating': df['rating'].mean(),
            'sparsity': 1 - len(df) / (df['user_id'].nunique() * df['movie_id'].nunique()),
            'interactions_per_user': df.groupby('user_id').size().describe(),
            'interactions_per_movie': df.groupby('movie_id').size().describe()
        }
        return stats
    
    def print_data_info(self, df: pd.DataFrame):
        """
        打印数据信息
        
        Args:
            df: 数据DataFrame
        """
        stats = self.get_data_statistics(df)
        
        print("\n📊 数据集统计信息:")
        print(f"   总交互数: {stats['total_interactions']:,}")
        print(f"   用户数: {stats['unique_users']:,}")
        print(f"   电影数: {stats['unique_movies']:,}")
        print(f"   评分范围: {stats['rating_range'][0]} - {stats['rating_range'][1]}")
        print(f"   平均评分: {stats['avg_rating']:.2f}")
        print(f"   稀疏度: {stats['sparsity']:.4f}")
        print(f"   每用户平均交互: {stats['interactions_per_user']['mean']:.1f}")
        print(f"   每电影平均交互: {stats['interactions_per_movie']['mean']:.1f}")
        
        print("\n📋 数据样例:")
        print(df.head())
        
        print("\n🔍 数据类型:")
        print(df.dtypes)
    
    def save_processed_data(self, df: pd.DataFrame, output_path: str):
        """
        保存预处理后的数据
        
        Args:
            df: 数据DataFrame
            output_path: 输出文件路径
        """
        df.to_csv(output_path, index=False)
        print(f"✅ 数据已保存到: {output_path}")
    
    def load_processed_data(self, input_path: str) -> pd.DataFrame:
        """
        加载预处理后的数据
        
        Args:
            input_path: 输入文件路径
            
        Returns:
            pd.DataFrame: 加载的数据
        """
        df = pd.read_csv(input_path)
        print(f"✅ 数据已从 {input_path} 加载")
        return df
