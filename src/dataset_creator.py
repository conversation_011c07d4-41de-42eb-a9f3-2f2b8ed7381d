"""
数据集创建模块

负责创建传统的pairwise数据集和改进的pointwise数据集
"""

import numpy as np
import pandas as pd
import tensorflow as tf
from typing import Dict, List, Tuple, Optional, Set


class DatasetCreator:
    """数据集创建器"""
    
    def __init__(self):
        """初始化数据集创建器"""
        pass
    
    def create_traditional_dataset(self, df: pd.DataFrame, batch_size: int = 128) -> tf.data.Dataset:
        """
        创建传统的训练数据集 (Pairwise)
        
        Args:
            df: 数据DataFrame
            batch_size: 批次大小
            
        Returns:
            tf.data.Dataset: 传统训练数据集
        """
        # 转换为列表以避免pandas迭代问题
        data_list = []
        for _, row in df.iterrows():
            features = {
                'user_id': int(row['user_id']),
                'movie_id': int(row['movie_id']),
                'gender': int(row['gender']),
                'age': int(row['age']),
                'occupation': int(row['occupation']),
                'main_genre': str(row['main_genre']),
                'year': float(row['year'])
            }
            label = float(row['rating'])
            data_list.append((features, label))
        
        def generator():
            for features, label in data_list:
                yield features, label
        
        # 定义输出签名
        output_signature = (
            {
                'user_id': tf.TensorSpec(shape=(), dtype=tf.int64),
                'movie_id': tf.TensorSpec(shape=(), dtype=tf.int64),
                'gender': tf.TensorSpec(shape=(), dtype=tf.int64),
                'age': tf.TensorSpec(shape=(), dtype=tf.int64),
                'occupation': tf.TensorSpec(shape=(), dtype=tf.int64),
                'main_genre': tf.TensorSpec(shape=(), dtype=tf.string),
                'year': tf.TensorSpec(shape=(), dtype=tf.float32)
            },
            tf.TensorSpec(shape=(), dtype=tf.float32)
        )
        
        dataset = tf.data.Dataset.from_generator(
            generator,
            output_signature=output_signature
        )
        
        return dataset.batch(batch_size).prefetch(tf.data.AUTOTUNE)
    
    def create_pointwise_dataset(self, df: pd.DataFrame, vocabs: Dict[str, List], 
                               item_popularity: Dict[int, float], 
                               item_log_prob: Dict[int, float],
                               neg_ratio: int = 2, batch_size: int = 128) -> tf.data.Dataset:
        """
        创建pointwise训练数据集，包含正负样本和偏差修正
        
        Args:
            df: 数据DataFrame
            vocabs: 词汇表字典
            item_popularity: 物品流行度字典
            item_log_prob: 物品对数概率字典
            neg_ratio: 负样本比例
            batch_size: 批次大小
            
        Returns:
            tf.data.Dataset: Pointwise训练数据集
        """
        # 获取所有物品ID
        all_items = set(vocabs['movie_id'])
        
        # 预计算用户交互物品
        user_items_dict = {}
        for user_id in df['user_id'].unique():
            user_items_dict[user_id] = set(df[df['user_id'] == user_id]['movie_id'].values)
        
        data_list = []
        
        for _, row in df.iterrows():
            user_id = row['user_id']
            
            # 获取用户已交互的物品
            user_items = user_items_dict[user_id]
            
            # 正样本
            pos_features = {
                'user_id': int(row['user_id']),
                'movie_id': int(row['movie_id']),
                'gender': int(row['gender']),
                'age': int(row['age']),
                'occupation': int(row['occupation']),
                'main_genre': str(row['main_genre']),
                'year': float(row['year']),
                'label': 1,  # 正样本标签
                'bias_correction': 0.0  # 正样本不需要偏差修正
            }
            data_list.append((pos_features, float(row['rating'])))
            
            # 负样本
            candidate_items = list(all_items - user_items)
            if len(candidate_items) >= neg_ratio:
                # 基于流行度采样负样本
                probs = np.array([item_popularity.get(item, 1e-10) for item in candidate_items])
                probs = probs / probs.sum()
                
                try:
                    neg_items = np.random.choice(
                        candidate_items, 
                        size=neg_ratio, 
                        replace=False,
                        p=probs
                    )
                except ValueError:
                    # 如果概率有问题，使用均匀采样
                    neg_items = np.random.choice(
                        candidate_items, 
                        size=neg_ratio, 
                        replace=False
                    )
                
                for neg_item in neg_items:
                    # 查找负样本物品信息
                    neg_item_rows = df[df['movie_id'] == neg_item]
                    if len(neg_item_rows) > 0:
                        neg_item_info = neg_item_rows.iloc[0]
                    else:
                        neg_item_info = row  # 使用当前行作为默认值
                    
                    neg_features = {
                        'user_id': int(row['user_id']),
                        'movie_id': int(neg_item),
                        'gender': int(row['gender']),
                        'age': int(row['age']),
                        'occupation': int(row['occupation']),
                        'main_genre': str(neg_item_info['main_genre']),
                        'year': float(neg_item_info['year']),
                        'label': 0,  # 负样本标签
                        'bias_correction': float(item_log_prob.get(neg_item, 0.0))  # 偏差修正项
                    }
                    data_list.append((neg_features, 0.0))  # 负样本评分为0
        
        def generator():
            # 打乱数据
            np.random.shuffle(data_list)
            for features, label in data_list:
                yield features, label
        
        # 定义输出签名
        output_signature = (
            {
                'user_id': tf.TensorSpec(shape=(), dtype=tf.int64),
                'movie_id': tf.TensorSpec(shape=(), dtype=tf.int64),
                'gender': tf.TensorSpec(shape=(), dtype=tf.int64),
                'age': tf.TensorSpec(shape=(), dtype=tf.int64),
                'occupation': tf.TensorSpec(shape=(), dtype=tf.int64),
                'main_genre': tf.TensorSpec(shape=(), dtype=tf.string),
                'year': tf.TensorSpec(shape=(), dtype=tf.float32),
                'label': tf.TensorSpec(shape=(), dtype=tf.int32),
                'bias_correction': tf.TensorSpec(shape=(), dtype=tf.float32)
            },
            tf.TensorSpec(shape=(), dtype=tf.float32)
        )
        
        dataset = tf.data.Dataset.from_generator(
            generator,
            output_signature=output_signature
        )
        
        return dataset.batch(batch_size).prefetch(tf.data.AUTOTUNE)
    
    def analyze_dataset_balance(self, dataset: tf.data.Dataset, dataset_name: str):
        """
        分析数据集的正负样本平衡情况
        
        Args:
            dataset: 数据集
            dataset_name: 数据集名称
        """
        print(f"\n📊 {dataset_name} 数据集分析:")
        
        total_samples = 0
        positive_samples = 0
        negative_samples = 0
        bias_corrections = []
        
        # 分析几个批次
        for batch_features, batch_labels in dataset.take(5):
            batch_size = len(batch_labels)
            total_samples += batch_size
            
            if 'label' in batch_features:
                # Pointwise数据集
                pos_count = tf.reduce_sum(batch_features['label']).numpy()
                positive_samples += pos_count
                negative_samples += (batch_size - pos_count)
                
                bias_corrections.extend(batch_features['bias_correction'].numpy())
            else:
                # 传统数据集，所有样本都是正样本
                positive_samples += batch_size
        
        print(f"   总样本数 (前5个批次): {total_samples}")
        print(f"   正样本数: {positive_samples}")
        print(f"   负样本数: {negative_samples}")
        
        if negative_samples > 0:
            ratio = positive_samples / negative_samples
            print(f"   正负样本比例: 1:{negative_samples/positive_samples:.1f}")
            
            if bias_corrections:
                bias_corrections = np.array(bias_corrections)
                pos_bias = bias_corrections[bias_corrections == 0.0]
                neg_bias = bias_corrections[bias_corrections != 0.0]
                
                print(f"   偏差修正统计:")
                print(f"     正样本偏差修正: {len(pos_bias)} 个 (均为 0.0)")
                print(f"     负样本偏差修正: {len(neg_bias)} 个")
                if len(neg_bias) > 0:
                    print(f"     负样本偏差修正范围: {neg_bias.min():.4f} ~ {neg_bias.max():.4f}")
                    print(f"     负样本偏差修正均值: {neg_bias.mean():.4f}")
        else:
            print(f"   数据集类型: 传统pairwise (仅正样本)")
    
    def create_evaluation_dataset(self, df: pd.DataFrame, batch_size: int = 64) -> tf.data.Dataset:
        """
        创建评估数据集
        
        Args:
            df: 数据DataFrame
            batch_size: 批次大小
            
        Returns:
            tf.data.Dataset: 评估数据集
        """
        return self.create_traditional_dataset(df, batch_size)
    
    def get_dataset_info(self, dataset: tf.data.Dataset) -> Dict[str, any]:
        """
        获取数据集信息
        
        Args:
            dataset: 数据集
            
        Returns:
            Dict[str, any]: 数据集信息
        """
        # 获取一个批次来分析结构
        sample_batch = next(iter(dataset))
        features, labels = sample_batch
        
        info = {
            'batch_size': len(labels),
            'feature_fields': list(features.keys()),
            'label_type': labels.dtype,
            'label_shape': labels.shape,
            'has_bias_correction': 'bias_correction' in features,
            'has_binary_labels': 'label' in features
        }
        
        return info
    
    def save_dataset_sample(self, dataset: tf.data.Dataset, output_path: str, num_samples: int = 100):
        """
        保存数据集样本用于调试
        
        Args:
            dataset: 数据集
            output_path: 输出文件路径
            num_samples: 保存的样本数量
        """
        samples = []
        count = 0
        
        for batch_features, batch_labels in dataset:
            batch_size = len(batch_labels)
            
            for i in range(batch_size):
                if count >= num_samples:
                    break
                
                sample = {}
                for key, value in batch_features.items():
                    sample[key] = value[i].numpy()
                sample['rating'] = batch_labels[i].numpy()
                
                samples.append(sample)
                count += 1
            
            if count >= num_samples:
                break
        
        # 保存为CSV
        df = pd.DataFrame(samples)
        df.to_csv(output_path, index=False)
        print(f"✅ 数据集样本已保存到: {output_path}")
