"""
结果可视化模块

负责生成各种图表来展示分析结果和模型效果
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional, Any

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")


class ResultVisualizer:
    """结果可视化器"""
    
    def __init__(self):
        """初始化可视化器"""
        pass
    
    def plot_popularity_distribution(self, item_popularity: Dict[int, float], 
                                   item_log_prob: Dict[int, float], 
                                   save_path: str = None):
        """
        可视化物品流行度分布
        
        Args:
            item_popularity: 物品流行度字典
            item_log_prob: 物品对数概率字典
            save_path: 保存路径
        """
        popularities = list(item_popularity.values())
        log_probs = list(item_log_prob.values())
        
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        # 流行度分布直方图
        axes[0].hist(popularities, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0].set_xlabel('物品流行度')
        axes[0].set_ylabel('物品数量')
        axes[0].set_title('物品流行度分布')
        axes[0].set_yscale('log')
        
        # log概率分布
        axes[1].hist(log_probs, bins=50, alpha=0.7, color='lightcoral', edgecolor='black')
        axes[1].set_xlabel('log(pi)')
        axes[1].set_ylabel('物品数量')
        axes[1].set_title('log概率分布')
        
        # 长尾分布
        sorted_pops = sorted(popularities, reverse=True)
        axes[2].plot(range(len(sorted_pops)), sorted_pops, 'b-', alpha=0.7, linewidth=2)
        axes[2].set_xlabel('物品排名')
        axes[2].set_ylabel('流行度')
        axes[2].set_title('长尾分布')
        axes[2].set_yscale('log')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"✅ 流行度分布图已保存到: {save_path}")
        
        plt.show()
    
    def plot_training_comparison(self, traditional_history, improved_history, 
                               save_path: str = None):
        """
        可视化训练过程对比
        
        Args:
            traditional_history: 传统方法训练历史
            improved_history: 改进方法训练历史
            save_path: 保存路径
        """
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        # 训练损失对比
        axes[0].plot(traditional_history.history['loss'], 'b-', label='传统方法', linewidth=2)
        axes[0].plot(improved_history.history['loss'], 'r-', label='改进方法', linewidth=2)
        axes[0].set_xlabel('Epoch')
        axes[0].set_ylabel('训练损失')
        axes[0].set_title('训练损失对比')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)
        
        # 验证损失对比
        axes[1].plot(traditional_history.history['val_loss'], 'b-', label='传统方法', linewidth=2)
        axes[1].plot(improved_history.history['val_loss'], 'r-', label='改进方法', linewidth=2)
        axes[1].set_xlabel('Epoch')
        axes[1].set_ylabel('验证损失')
        axes[1].set_title('验证损失对比')
        axes[1].legend()
        axes[1].grid(True, alpha=0.3)
        
        # RMSE对比
        if 'rmse' in traditional_history.history and 'rmse' in improved_history.history:
            axes[2].plot(traditional_history.history['rmse'], 'b-', label='传统方法', linewidth=2)
            axes[2].plot(improved_history.history['rmse'], 'r-', label='改进方法', linewidth=2)
            axes[2].set_xlabel('Epoch')
            axes[2].set_ylabel('RMSE')
            axes[2].set_title('RMSE对比')
            axes[2].legend()
            axes[2].grid(True, alpha=0.3)
        else:
            axes[2].text(0.5, 0.5, 'RMSE数据不可用', ha='center', va='center', transform=axes[2].transAxes)
            axes[2].set_title('RMSE对比')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"✅ 训练对比图已保存到: {save_path}")
        
        plt.show()
    
    def plot_recommendation_comparison(self, traditional_recs: List[Dict[str, Any]], 
                                    improved_recs: List[Dict[str, Any]], 
                                    item_popularity: Dict[int, float], 
                                    save_path: str = None):
        """
        可视化推荐结果对比
        
        Args:
            traditional_recs: 传统方法推荐结果
            improved_recs: 改进方法推荐结果
            item_popularity: 物品流行度字典
            save_path: 保存路径
        """
        # 提取流行度数据
        traditional_popularities = [
            item_popularity.get(rec['movie_info']['movie_id'], 0) 
            for rec in traditional_recs
        ]
        improved_popularities = [
            item_popularity.get(rec['movie_info']['movie_id'], 0) 
            for rec in improved_recs
        ]
        
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        # 流行度分布对比
        x = range(1, len(traditional_popularities) + 1)
        axes[0].plot(x, traditional_popularities, 'bo-', label='传统方法', linewidth=2, markersize=8)
        axes[0].plot(x, improved_popularities, 'ro-', label='改进方法', linewidth=2, markersize=8)
        axes[0].set_xlabel('推荐排名')
        axes[0].set_ylabel('物品流行度')
        axes[0].set_title('推荐物品流行度对比')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)
        
        # 流行度分布直方图
        axes[1].hist(traditional_popularities, bins=5, alpha=0.7, color='blue', 
                    label='传统方法', edgecolor='black')
        axes[1].hist(improved_popularities, bins=5, alpha=0.7, color='red', 
                    label='改进方法', edgecolor='black')
        axes[1].set_xlabel('物品流行度')
        axes[1].set_ylabel('推荐数量')
        axes[1].set_title('推荐流行度分布')
        axes[1].legend()
        
        # 统计对比
        metrics = ['平均流行度', '流行度标准差', '最高流行度', '最低流行度']
        traditional_stats = [
            np.mean(traditional_popularities),
            np.std(traditional_popularities),
            max(traditional_popularities),
            min(traditional_popularities)
        ]
        improved_stats = [
            np.mean(improved_popularities),
            np.std(improved_popularities),
            max(improved_popularities),
            min(improved_popularities)
        ]
        
        x = np.arange(len(metrics))
        width = 0.35
        axes[2].bar(x - width/2, traditional_stats, width, label='传统方法', 
                   color='blue', alpha=0.7)
        axes[2].bar(x + width/2, improved_stats, width, label='改进方法', 
                   color='red', alpha=0.7)
        axes[2].set_xlabel('指标')
        axes[2].set_ylabel('数值')
        axes[2].set_title('推荐统计对比')
        axes[2].set_xticks(x)
        axes[2].set_xticklabels(metrics, rotation=45)
        axes[2].legend()
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"✅ 推荐对比图已保存到: {save_path}")
        
        plt.show()
    
    def plot_bias_correction_analysis(self, bias_corrections: List[float], 
                                    labels: List[int], save_path: str = None):
        """
        可视化偏差修正分析
        
        Args:
            bias_corrections: 偏差修正值列表
            labels: 对应的标签列表 (0=负样本, 1=正样本)
            save_path: 保存路径
        """
        bias_corrections = np.array(bias_corrections)
        labels = np.array(labels)
        
        # 分别分析正样本和负样本的偏差修正
        pos_bias = bias_corrections[labels == 1]
        neg_bias = bias_corrections[labels == 0]
        
        fig, axes = plt.subplots(1, 2, figsize=(12, 4))
        
        # 偏差修正值分布
        axes[0].hist(pos_bias, bins=30, alpha=0.7, color='green', label='正样本', edgecolor='black')
        axes[0].hist(neg_bias, bins=30, alpha=0.7, color='red', label='负样本', edgecolor='black')
        axes[0].set_xlabel('偏差修正值 (log(pi))')
        axes[0].set_ylabel('样本数量')
        axes[0].set_title('偏差修正值分布')
        axes[0].legend()
        
        # 箱线图
        axes[1].boxplot([pos_bias, neg_bias], labels=['正样本', '负样本'])
        axes[1].set_ylabel('偏差修正值 (log(pi))')
        axes[1].set_title('偏差修正值箱线图')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"✅ 偏差修正分析图已保存到: {save_path}")
        
        plt.show()
    
    def plot_evaluation_metrics(self, results: Dict[str, Any], save_path: str = None):
        """
        可视化评估指标
        
        Args:
            results: 评估结果字典
            save_path: 保存路径
        """
        traditional = results['traditional_method']
        improved = results['improved_method']
        
        # 选择关键指标
        metrics = ['avg_popularity', 'popularity_std', 'genre_diversity', 'long_tail_coverage']
        metric_names = ['平均流行度', '流行度标准差', '类型多样性', '长尾覆盖率']
        
        traditional_values = [traditional[metric] for metric in metrics]
        improved_values = [improved[metric] for metric in metrics]
        
        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        axes = axes.flatten()
        
        for i, (metric, name) in enumerate(zip(metrics, metric_names)):
            x = ['传统方法', '改进方法']
            y = [traditional_values[i], improved_values[i]]
            
            bars = axes[i].bar(x, y, color=['blue', 'red'], alpha=0.7)
            axes[i].set_title(name)
            axes[i].set_ylabel('数值')
            
            # 添加数值标签
            for bar, value in zip(bars, y):
                height = bar.get_height()
                axes[i].text(bar.get_x() + bar.get_width()/2., height,
                           f'{value:.4f}', ha='center', va='bottom')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"✅ 评估指标图已保存到: {save_path}")
        
        plt.show()
    
    def create_summary_report(self, results: Dict[str, Any], output_dir: str):
        """
        创建总结报告
        
        Args:
            results: 评估结果
            output_dir: 输出目录
        """
        # 创建一个综合的总结图
        fig = plt.figure(figsize=(16, 12))
        
        # 创建网格布局
        gs = fig.add_gridspec(3, 3, hspace=0.3, wspace=0.3)
        
        traditional = results['traditional_method']
        improved = results['improved_method']
        
        # 1. 流行度对比
        ax1 = fig.add_subplot(gs[0, 0])
        metrics = ['平均流行度', '流行度标准差']
        trad_vals = [traditional['avg_popularity'], traditional['popularity_std']]
        impr_vals = [improved['avg_popularity'], improved['popularity_std']]
        
        x = np.arange(len(metrics))
        width = 0.35
        ax1.bar(x - width/2, trad_vals, width, label='传统方法', alpha=0.7)
        ax1.bar(x + width/2, impr_vals, width, label='改进方法', alpha=0.7)
        ax1.set_title('流行度指标对比')
        ax1.set_xticks(x)
        ax1.set_xticklabels(metrics, rotation=45)
        ax1.legend()
        
        # 2. 多样性对比
        ax2 = fig.add_subplot(gs[0, 1])
        diversity_metrics = ['类型多样性', '年份多样性']
        trad_div = [traditional['genre_diversity'], traditional.get('year_diversity', 0)]
        impr_div = [improved['genre_diversity'], improved.get('year_diversity', 0)]
        
        x = np.arange(len(diversity_metrics))
        ax2.bar(x - width/2, trad_div, width, label='传统方法', alpha=0.7)
        ax2.bar(x + width/2, impr_div, width, label='改进方法', alpha=0.7)
        ax2.set_title('多样性指标对比')
        ax2.set_xticks(x)
        ax2.set_xticklabels(diversity_metrics, rotation=45)
        ax2.legend()
        
        # 3. 长尾覆盖对比
        ax3 = fig.add_subplot(gs[0, 2])
        coverage_data = [traditional['long_tail_coverage'], improved['long_tail_coverage']]
        colors = ['blue', 'red']
        ax3.pie(coverage_data, labels=['传统方法', '改进方法'], colors=colors, 
               autopct='%1.1f%%', alpha=0.7)
        ax3.set_title('长尾覆盖率对比')
        
        # 4. 改进效果总结
        ax4 = fig.add_subplot(gs[1, :])
        improvement = results['improvement']
        summary = results['comparison_summary']
        
        # 创建改进效果文本
        summary_text = "改进效果总结:\n\n"
        for key, value in summary.items():
            summary_text += f"• {key}: {value}\n"
        
        ax4.text(0.1, 0.9, summary_text, transform=ax4.transAxes, 
                fontsize=12, verticalalignment='top', 
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.5))
        ax4.set_xlim(0, 1)
        ax4.set_ylim(0, 1)
        ax4.axis('off')
        ax4.set_title('改进效果总结', fontsize=14, fontweight='bold')
        
        # 5. 关键指标雷达图
        ax5 = fig.add_subplot(gs[2, :], projection='polar')
        
        # 准备雷达图数据
        categories = ['流行度均衡', '多样性', '长尾覆盖', '预测准确性']
        
        # 标准化指标 (0-1之间)
        trad_radar = [
            1 - min(traditional['avg_popularity'] * 1000, 1),  # 流行度越低越好
            traditional['genre_diversity'],
            traditional['long_tail_coverage'],
            1 - min(traditional.get('avg_rating', 3) / 5, 1)  # 评分接近中等最好
        ]
        
        impr_radar = [
            1 - min(improved['avg_popularity'] * 1000, 1),
            improved['genre_diversity'],
            improved['long_tail_coverage'],
            1 - min(improved.get('avg_rating', 3) / 5, 1)
        ]
        
        # 计算角度
        angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
        angles += angles[:1]  # 闭合
        
        trad_radar += trad_radar[:1]
        impr_radar += impr_radar[:1]
        
        ax5.plot(angles, trad_radar, 'o-', linewidth=2, label='传统方法', color='blue')
        ax5.fill(angles, trad_radar, alpha=0.25, color='blue')
        ax5.plot(angles, impr_radar, 'o-', linewidth=2, label='改进方法', color='red')
        ax5.fill(angles, impr_radar, alpha=0.25, color='red')
        
        ax5.set_xticks(angles[:-1])
        ax5.set_xticklabels(categories)
        ax5.set_ylim(0, 1)
        ax5.set_title('综合性能雷达图', pad=20)
        ax5.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
        
        plt.suptitle('改进的负样本采集策略 - 效果评估报告', fontsize=16, fontweight='bold')
        
        # 保存报告
        report_path = os.path.join(output_dir, 'summary_report.png')
        plt.savefig(report_path, dpi=300, bbox_inches='tight')
        print(f"✅ 总结报告已保存到: {report_path}")
        
        plt.show()
