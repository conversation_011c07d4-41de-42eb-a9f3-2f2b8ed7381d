#!/usr/bin/env python3
"""
改进的负样本采集策略演示 - 主程序

基于业界最佳实践，对双塔推荐系统进行以下改进：
1. Pointwise训练: 采用1:2或1:3的正负样本比例
2. 热门偏差修正: 训练时使用 cos(a,bi) - log(pi) 修正热门偏差
3. 推理时还原: 推理时使用原始 cos(a,bi) 进行召回
4. 流行度感知: 基于点击次数的负样本采样
"""

import os
import sys
import argparse
import warnings
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.data_loader import MovieLensDataLoader
from src.popularity_analyzer import PopularityAnalyzer
from src.dataset_creator import DatasetCreator
from src.two_tower_model import TwoTowerModel
from src.recommender import RecommendationEngine
from src.evaluator import ModelEvaluator
from src.visualizer import ResultVisualizer

warnings.filterwarnings('ignore')

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='改进的负样本采集策略演示')
    
    parser.add_argument('--data_dir', type=str, default='data/ml-1m',
                       help='MovieLens数据集路径')
    parser.add_argument('--sample_size', type=int, default=10000,
                       help='演示用的样本数量')
    parser.add_argument('--neg_ratio', type=int, default=2,
                       help='负样本比例 (1:neg_ratio)')
    parser.add_argument('--embedding_dim', type=int, default=64,
                       help='嵌入维度')
    parser.add_argument('--epochs', type=int, default=5,
                       help='训练轮数')
    parser.add_argument('--learning_rate', type=float, default=0.001,
                       help='学习率')
    parser.add_argument('--batch_size', type=int, default=128,
                       help='批次大小')
    parser.add_argument('--k_recall', type=int, default=20,
                       help='召回阶段Top-K')
    parser.add_argument('--k_rank', type=int, default=10,
                       help='排序阶段Top-K')
    parser.add_argument('--output_dir', type=str, default='output',
                       help='输出目录')
    parser.add_argument('--mode', type=str, default='full',
                       choices=['full', 'train_only', 'eval_only'],
                       help='运行模式')
    
    return parser.parse_args()

def setup_output_directory(output_dir: str):
    """创建输出目录"""
    os.makedirs(output_dir, exist_ok=True)
    os.makedirs(os.path.join(output_dir, 'models'), exist_ok=True)
    os.makedirs(os.path.join(output_dir, 'plots'), exist_ok=True)
    os.makedirs(os.path.join(output_dir, 'results'), exist_ok=True)

def load_and_preprocess_data(args):
    """加载和预处理数据"""
    print("🎬 加载MovieLens数据集...")
    
    # 加载数据
    loader = MovieLensDataLoader(args.data_dir)
    if not loader.load_data():
        print("❌ 数据加载失败！")
        sys.exit(1)
    
    # 预处理数据
    df = loader.preprocess_data()
    vocabs = loader.get_vocabularies(df)
    
    print(f"✅ 数据加载成功！")
    print(f"   用户数: {len(vocabs['user_id']):,}")
    print(f"   电影数: {len(vocabs['movie_id']):,}")
    print(f"   交互数: {len(df):,}")
    print(f"   评分范围: {df['rating'].min()} - {df['rating'].max()}")
    
    # 为演示使用样本数据
    if args.sample_size < len(df):
        print(f"\n🎯 使用 {args.sample_size:,} 条记录进行演示...")
        df = df.sample(n=args.sample_size, random_state=42)
        vocabs = loader.get_vocabularies(df)
    
    # 划分训练测试集
    train_size = int(0.8 * len(df))
    train_df = df.sample(n=train_size, random_state=42)
    test_df = df.drop(train_df.index)
    
    print(f"   训练集: {len(train_df):,} 条记录")
    print(f"   测试集: {len(test_df):,} 条记录")
    
    return df, train_df, test_df, vocabs

def analyze_popularity(train_df, output_dir):
    """分析物品流行度"""
    print("\n📊 分析物品流行度分布...")
    
    analyzer = PopularityAnalyzer()
    item_popularity, item_log_prob = analyzer.compute_item_popularity(train_df)
    
    # 生成分析报告
    analyzer.print_popularity_stats(item_popularity, item_log_prob)
    
    # 可视化流行度分布
    visualizer = ResultVisualizer()
    visualizer.plot_popularity_distribution(
        item_popularity, item_log_prob,
        save_path=os.path.join(output_dir, 'plots', 'popularity_distribution.png')
    )
    
    return item_popularity, item_log_prob

def create_datasets(train_df, test_df, vocabs, item_popularity, item_log_prob, args):
    """创建训练和测试数据集"""
    print("\n🚀 创建数据集...")
    
    creator = DatasetCreator()
    
    # 传统数据集 (Pairwise)
    traditional_train_ds = creator.create_traditional_dataset(
        train_df, batch_size=args.batch_size
    )
    traditional_test_ds = creator.create_traditional_dataset(
        test_df, batch_size=args.batch_size//2
    )
    
    # 改进数据集 (Pointwise + 偏差修正)
    improved_train_ds = creator.create_pointwise_dataset(
        train_df, vocabs, item_popularity, item_log_prob,
        neg_ratio=args.neg_ratio, batch_size=args.batch_size
    )
    improved_test_ds = creator.create_traditional_dataset(
        test_df, batch_size=args.batch_size//2
    )
    
    print(f"✅ 数据集创建完成")
    print(f"   负样本比例: 1:{args.neg_ratio}")
    print(f"   批次大小: {args.batch_size}")
    
    return traditional_train_ds, traditional_test_ds, improved_train_ds, improved_test_ds

def train_models(datasets, vocabs, args, output_dir):
    """训练传统模型和改进模型"""
    traditional_train_ds, traditional_test_ds, improved_train_ds, improved_test_ds = datasets
    
    print("\n🏋️ 开始模型训练对比...")
    print("=" * 60)
    
    # 训练传统模型
    print("\n🔸 训练传统模型 (Pairwise)...")
    traditional_model = TwoTowerModel(vocabs, embedding_dim=args.embedding_dim)
    traditional_model.initialize_with_data(traditional_train_ds)
    
    traditional_history = traditional_model.train(
        traditional_train_ds, traditional_test_ds,
        epochs=args.epochs, learning_rate=args.learning_rate
    )
    
    # 保存传统模型
    traditional_model.save(os.path.join(output_dir, 'models', 'traditional_model'))
    
    # 训练改进模型
    print("\n🔹 训练改进模型 (Pointwise + 偏差修正)...")
    improved_model = TwoTowerModel(vocabs, embedding_dim=args.embedding_dim)
    improved_model.initialize_with_data(improved_train_ds)
    
    improved_history = improved_model.train(
        improved_train_ds, improved_test_ds,
        epochs=args.epochs, learning_rate=args.learning_rate
    )
    
    # 保存改进模型
    improved_model.save(os.path.join(output_dir, 'models', 'improved_model'))
    
    # 可视化训练过程
    visualizer = ResultVisualizer()
    visualizer.plot_training_comparison(
        traditional_history, improved_history,
        save_path=os.path.join(output_dir, 'plots', 'training_comparison.png')
    )
    
    return traditional_model, improved_model, traditional_history, improved_history

def evaluate_models(traditional_model, improved_model, test_df, vocabs, 
                   item_popularity, args, output_dir):
    """评估模型推荐效果"""
    print("\n🎯 推荐效果对比测试...")
    print("=" * 60)
    
    evaluator = ModelEvaluator()
    recommender = RecommendationEngine()
    
    # 选择测试用户
    test_user = test_df.iloc[0]
    user_features = {
        'user_id': test_user['user_id'],
        'gender': test_user['gender'],
        'age': test_user['age'],
        'occupation': test_user['occupation']
    }
    
    # 准备候选电影
    candidate_movies = evaluator.prepare_candidate_movies(
        test_df, item_popularity, n_candidates=30
    )
    
    # 传统方法推荐
    traditional_recs = recommender.recommend(
        traditional_model, user_features, candidate_movies,
        k_recall=args.k_recall, k_rank=args.k_rank
    )
    
    # 改进方法推荐
    improved_recs = recommender.recommend(
        improved_model, user_features, candidate_movies,
        k_recall=args.k_recall, k_rank=args.k_rank
    )
    
    # 评估推荐结果
    results = evaluator.compare_recommendations(
        traditional_recs, improved_recs, item_popularity
    )
    
    # 可视化推荐结果
    visualizer = ResultVisualizer()
    visualizer.plot_recommendation_comparison(
        traditional_recs, improved_recs, item_popularity,
        save_path=os.path.join(output_dir, 'plots', 'recommendation_comparison.png')
    )
    
    # 保存评估结果
    evaluator.save_results(results, os.path.join(output_dir, 'results', 'evaluation_results.json'))
    
    return results

def main():
    """主函数"""
    args = parse_arguments()
    
    print("📋 改进的负样本采集策略演示")
    print("=" * 80)
    print(f"运行模式: {args.mode}")
    print(f"数据路径: {args.data_dir}")
    print(f"样本数量: {args.sample_size:,}")
    print(f"负样本比例: 1:{args.neg_ratio}")
    print(f"嵌入维度: {args.embedding_dim}")
    print(f"训练轮数: {args.epochs}")
    
    # 设置输出目录
    setup_output_directory(args.output_dir)
    
    try:
        # 1. 加载和预处理数据
        df, train_df, test_df, vocabs = load_and_preprocess_data(args)
        
        # 2. 分析物品流行度
        item_popularity, item_log_prob = analyze_popularity(train_df, args.output_dir)
        
        if args.mode in ['full', 'train_only']:
            # 3. 创建数据集
            datasets = create_datasets(
                train_df, test_df, vocabs, item_popularity, item_log_prob, args
            )
            
            # 4. 训练模型
            traditional_model, improved_model, traditional_history, improved_history = train_models(
                datasets, vocabs, args, args.output_dir
            )
        
        if args.mode in ['full', 'eval_only']:
            # 如果是仅评估模式，加载已训练的模型
            if args.mode == 'eval_only':
                traditional_model = TwoTowerModel.load(
                    os.path.join(args.output_dir, 'models', 'traditional_model')
                )
                improved_model = TwoTowerModel.load(
                    os.path.join(args.output_dir, 'models', 'improved_model')
                )
            
            # 5. 评估模型
            results = evaluate_models(
                traditional_model, improved_model, test_df, vocabs,
                item_popularity, args, args.output_dir
            )
            
            print("\n🎉 评估完成！")
            print(f"结果保存在: {args.output_dir}")
        
        print("\n✅ 程序执行完成！")
        
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
